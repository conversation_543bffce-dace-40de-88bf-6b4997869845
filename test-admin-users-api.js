const axios = require('axios');

// Test the admin users API to check subscription data
async function testAdminUsersAPI() {
  try {
    console.log('🧪 Testing Admin Users API...\n');
    
    // You'll need to get a valid admin token first
    // For now, let's test the endpoint structure
    
    const baseURL = 'http://localhost:5000';
    
    // Test the get-all-users endpoint
    console.log('📡 Testing /api/users/get-all-users endpoint...');
    
    try {
      const response = await axios.get(`${baseURL}/api/users/get-all-users`);
      
      if (response.data.success) {
        console.log('✅ API call successful');
        console.log(`📊 Total users returned: ${response.data.users.length}`);
        
        if (response.data.users.length > 0) {
          console.log('\n🔍 First user structure:');
          const firstUser = response.data.users[0];
          console.log('Available fields:', Object.keys(firstUser));
          
          console.log('\n📋 User details:');
          console.log(`  Name: ${firstUser.name}`);
          console.log(`  Email: ${firstUser.email}`);
          console.log(`  Subscription Status: ${firstUser.subscriptionStatus || 'undefined'}`);
          console.log(`  Subscription Plan: ${firstUser.subscriptionPlan || 'undefined'}`);
          console.log(`  Subscription End Date: ${firstUser.subscriptionEndDate || 'undefined'}`);
          console.log(`  Payment Required: ${firstUser.paymentRequired || 'undefined'}`);
          
          // Check how many users have subscription data
          const usersWithSubscription = response.data.users.filter(user => 
            user.subscriptionStatus && user.subscriptionStatus !== 'free'
          );
          
          const usersWithPlan = response.data.users.filter(user => 
            user.subscriptionPlan
          );
          
          const usersWithEndDate = response.data.users.filter(user => 
            user.subscriptionEndDate
          );
          
          console.log('\n📈 Subscription Data Summary:');
          console.log(`  Users with subscription status (not free): ${usersWithSubscription.length}`);
          console.log(`  Users with subscription plan: ${usersWithPlan.length}`);
          console.log(`  Users with subscription end date: ${usersWithEndDate.length}`);
          
          // Show subscription status distribution
          const statusDistribution = {};
          response.data.users.forEach(user => {
            const status = user.subscriptionStatus || 'undefined';
            statusDistribution[status] = (statusDistribution[status] || 0) + 1;
          });
          
          console.log('\n📊 Subscription Status Distribution:');
          Object.entries(statusDistribution).forEach(([status, count]) => {
            console.log(`  ${status}: ${count} users`);
          });
          
          // Test the filtering logic
          console.log('\n🧪 Testing Filter Logic:');
          
          const getSubscriptionFilterStatus = (user) => {
            const subscriptionStatus = user.subscriptionStatus || 'free';
            
            if (!subscriptionStatus || subscriptionStatus === 'free') {
              return 'no-plan';
            }
            
            if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {
              if (user.subscriptionEndDate) {
                const endDate = new Date(user.subscriptionEndDate);
                const now = new Date();
                if (endDate > now) {
                  return 'on-plan';
                } else {
                  return 'expired-plan';
                }
              }
              return 'on-plan';
            }
            
            if (subscriptionStatus === 'expired') {
              return 'expired-plan';
            }
            
            return 'no-plan';
          };
          
          const filterResults = {
            'on-plan': 0,
            'expired-plan': 0,
            'no-plan': 0
          };
          
          response.data.users.forEach(user => {
            const filterStatus = getSubscriptionFilterStatus(user);
            filterResults[filterStatus]++;
          });
          
          console.log('Filter Results:');
          Object.entries(filterResults).forEach(([filter, count]) => {
            console.log(`  ${filter}: ${count} users`);
          });
          
        } else {
          console.log('⚠️ No users found in response');
        }
        
      } else {
        console.log('❌ API call failed:', response.data.message);
      }
      
    } catch (apiError) {
      if (apiError.response) {
        console.log('❌ API Error:', apiError.response.status, apiError.response.data);
      } else {
        console.log('❌ Network Error:', apiError.message);
      }
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testAdminUsersAPI();
