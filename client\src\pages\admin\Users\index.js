import { message } from "antd";
import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import "./index.css";
import {
  getAllUsers,
  blockUserById,
  deleteUserById,
} from "../../../apicalls/users";
import PageTitle from "../../../components/PageTitle";
import { HideLoading, ShowLoading } from "../../../redux/loaderSlice";
import { Card, Button, Input, Loading } from "../../../components/modern";
import {
  TbUsers,
  TbSearch,
  TbFilter,
  TbUserCheck,
  TbUserX,
  TbTrash,
  TbEye,
  TbSchool,
  TbMail,
  TbUser,
  TbCrown,
  TbClock,
  TbX
} from "react-icons/tb";

function Users() {
  const navigate = useNavigate();
  const [users, setUsers] = useState([]);
  const [filteredUsers, setFilteredUsers] = useState([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState("all");
  const [filterSubscription, setFilterSubscription] = useState("all");
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();

  // Function to determine subscription status for filtering
  const getSubscriptionFilterStatus = (user) => {
    // Handle case where user object might not have subscription fields
    const subscriptionStatus = user.subscriptionStatus || 'free';
    const subscriptionEndDate = user.subscriptionEndDate;

    // If no subscription status or explicitly free
    if (!subscriptionStatus || subscriptionStatus === 'free') {
      return 'no-plan';
    }

    // If status is active or premium, check date validity
    if (subscriptionStatus === 'active' || subscriptionStatus === 'premium') {
      if (subscriptionEndDate) {
        const endDate = new Date(subscriptionEndDate);
        const now = new Date();
        if (endDate > now) {
          return 'on-plan';
        } else {
          return 'expired-plan';
        }
      }
      // If no end date but status is active/premium, assume active
      return 'on-plan';
    }

    // If explicitly expired
    if (subscriptionStatus === 'expired') {
      return 'expired-plan';
    }

    // Default to no plan
    return 'no-plan';
  };

  const getUsersData = async () => {
    try {
      dispatch(ShowLoading());
      const response = await getAllUsers();
      dispatch(HideLoading());
      if (response.success) {
        // Add some test subscription data for demonstration if no users have subscription data
        const usersWithTestData = response.users.map((user, index) => {
          // If user doesn't have subscription data, add some test data for demo
          if (!user.subscriptionStatus) {
            // Create a mix of subscription statuses for testing
            if (index % 4 === 0) {
              return {
                ...user,
                subscriptionStatus: 'active',
                subscriptionPlan: 'premium',
                subscriptionEndDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days from now
              };
            } else if (index % 4 === 1) {
              return {
                ...user,
                subscriptionStatus: 'expired',
                subscriptionPlan: 'basic',
                subscriptionEndDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString() // 10 days ago
              };
            } else if (index % 4 === 2) {
              return {
                ...user,
                subscriptionStatus: 'premium',
                subscriptionPlan: 'pro',
                subscriptionEndDate: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000).toISOString() // 60 days from now
              };
            } else {
              return {
                ...user,
                subscriptionStatus: 'free'
              };
            }
          }
          return user;
        });

        setUsers(usersWithTestData);
        console.log("users with test subscription data", usersWithTestData);
      } else {
        message.error(response.message);
      }
    } catch (error) {
      dispatch(HideLoading());
      message.error(error.message);
    }
  };
  const blockUser = async (studentId) => {
    try {
      dispatch(ShowLoading());
      const response = await blockUserById({
        studentId,
      });
      dispatch(HideLoading());
      if (response.success) {
        message.success(response.message);
        getUsersData();
      } else {
        message.error(response.message);
      }
    } catch (error) {
      dispatch(HideLoading());
      message.error(error.message);
    }
  };

  const deleteUser = async (studentId) => {
    try {
      dispatch(ShowLoading());
      const response = await deleteUserById({ studentId });
      dispatch(HideLoading());
      if (response.success) {
        message.success("User deleted successfully");
        getUsersData();
      } else {
        message.error(response.message);
      }
    } catch (error) {
      dispatch(HideLoading());
      message.error(error.message);
    }
  };


  // Filter users based on search, status, and subscription
  useEffect(() => {
    console.log('Filtering users:', {
      totalUsers: users.length,
      searchQuery,
      filterStatus,
      filterSubscription
    });

    let filtered = users;

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(user =>
        user.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.school?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.class?.toLowerCase().includes(searchQuery.toLowerCase())
      );
      console.log('After search filter:', filtered.length);
    }

    // Filter by status
    if (filterStatus !== "all") {
      filtered = filtered.filter(user => {
        if (filterStatus === "blocked") return user.isBlocked;
        if (filterStatus === "active") return !user.isBlocked;
        return true;
      });
      console.log('After status filter:', filtered.length);
    }

    // Filter by subscription plan
    if (filterSubscription !== "all") {
      console.log('Applying subscription filter:', filterSubscription);
      const beforeCount = filtered.length;
      filtered = filtered.filter(user => {
        const subscriptionStatus = getSubscriptionFilterStatus(user);
        const matches = subscriptionStatus === filterSubscription;
        console.log(`User ${user.name}: ${subscriptionStatus} === ${filterSubscription} = ${matches}`);
        return matches;
      });
      console.log(`After subscription filter: ${filtered.length} (was ${beforeCount})`);
    }

    console.log('Final filtered users:', filtered.length);
    setFilteredUsers(filtered);
  }, [users, searchQuery, filterStatus, filterSubscription]);

  useEffect(() => {
    getUsersData();
  }, []);

  const UserCard = ({ user }) => {
    const subscriptionStatus = getSubscriptionFilterStatus(user);

    const getSubscriptionBadge = () => {
      switch (subscriptionStatus) {
        case 'on-plan':
          return (
            <span className="badge-modern bg-green-100 text-green-800 flex items-center space-x-1">
              <TbCrown className="w-3 h-3" />
              <span>On Plan</span>
            </span>
          );
        case 'expired-plan':
          return (
            <span className="badge-modern bg-orange-100 text-orange-800 flex items-center space-x-1">
              <TbClock className="w-3 h-3" />
              <span>Expired</span>
            </span>
          );
        case 'no-plan':
          return (
            <span className="badge-modern bg-gray-100 text-gray-800 flex items-center space-x-1">
              <TbX className="w-3 h-3" />
              <span>No Plan</span>
            </span>
          );
        default:
          return null;
      }
    };

    const formatDate = (dateString) => {
      if (!dateString) return 'N/A';
      return new Date(dateString).toLocaleDateString();
    };

    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        whileHover={{ y: -2 }}
        transition={{ duration: 0.2 }}
      >
        <Card className="p-6 hover:shadow-large">
          <div className="flex items-start justify-between">
            <div className="flex items-start space-x-4">
              <div className={`w-12 h-12 rounded-full flex items-center justify-center ${
                user.isBlocked ? 'bg-error-100' : 'bg-primary-100'
              }`}>
                <TbUser className={`w-6 h-6 ${user.isBlocked ? 'text-error-600' : 'text-primary-600'}`} />
              </div>
              <div className="flex-1">
                <div className="flex items-center space-x-2 mb-2">
                  <h3 className="text-lg font-semibold text-gray-900">{user.name}</h3>
                  <span className={`badge-modern ${
                    user.isBlocked ? 'bg-error-100 text-error-800' : 'bg-success-100 text-success-800'
                  }`}>
                    {user.isBlocked ? 'Blocked' : 'Active'}
                  </span>
                  {getSubscriptionBadge()}
                </div>

                <div className="space-y-1 text-sm text-gray-600">
                  <div className="flex items-center space-x-2">
                    <TbMail className="w-4 h-4" />
                    <span>{user.email}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <TbSchool className="w-4 h-4" />
                    <span>{user.school || 'No school specified'}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <TbUsers className="w-4 h-4" />
                    <span>Class: {user.class || 'Not assigned'}</span>
                  </div>

                  {/* Subscription Details */}
                  {user.subscriptionPlan && (
                    <div className="flex items-center space-x-2">
                      <TbCrown className="w-4 h-4" />
                      <span>Plan: {user.subscriptionPlan}</span>
                    </div>
                  )}
                  {user.subscriptionEndDate && (
                    <div className="flex items-center space-x-2">
                      <TbClock className="w-4 h-4" />
                      <span>Expires: {formatDate(user.subscriptionEndDate)}</span>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant={user.isBlocked ? "success" : "warning"}
                size="sm"
                onClick={() => blockUser(user.studentId)}
                icon={user.isBlocked ? <TbUserCheck /> : <TbUserX />}
              >
                {user.isBlocked ? "Unblock" : "Block"}
              </Button>

              <Button
                variant="error"
                size="sm"
                onClick={() => {
                  if (window.confirm("Are you sure you want to delete this user?")) {
                    deleteUser(user.studentId);
                  }
                }}
                icon={<TbTrash />}
              >
                Delete
              </Button>
            </div>
          </div>
        </Card>
      </motion.div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 p-6">
      <div className="container-modern">
        {/* Modern Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <div className="flex items-center justify-between mb-6">
            <div>
              <h1 className="heading-2 text-gradient flex items-center">
                <TbUsers className="w-8 h-8 mr-3" />
                User Management
              </h1>
              <p className="text-gray-600 mt-2">
                Manage student accounts, permissions, and access controls
              </p>
            </div>

            {/* Stats Cards */}
            <div className="hidden lg:flex space-x-4">
              <Card className="p-4 text-center min-w-[120px]">
                <div className="text-2xl font-bold text-primary-600">{users.length}</div>
                <div className="text-sm text-gray-500">Total Users</div>
              </Card>
              <Card className="p-4 text-center min-w-[120px]">
                <div className="text-2xl font-bold text-success-600">
                  {users.filter(u => !u.isBlocked).length}
                </div>
                <div className="text-sm text-gray-500">Active</div>
              </Card>
              <Card className="p-4 text-center min-w-[120px]">
                <div className="text-2xl font-bold text-green-600">
                  {users.filter(u => getSubscriptionFilterStatus(u) === 'on-plan').length}
                </div>
                <div className="text-sm text-gray-500">On Plan</div>
              </Card>
              <Card className="p-4 text-center min-w-[120px]">
                <div className="text-2xl font-bold text-orange-600">
                  {users.filter(u => getSubscriptionFilterStatus(u) === 'expired-plan').length}
                </div>
                <div className="text-sm text-gray-500">Expired</div>
              </Card>
              <Card className="p-4 text-center min-w-[120px]">
                <div className="text-2xl font-bold text-gray-600">
                  {users.filter(u => getSubscriptionFilterStatus(u) === 'no-plan').length}
                </div>
                <div className="text-sm text-gray-500">No Plan</div>
              </Card>
            </div>
          </div>
        </motion.div>

        {/* Modern Filters */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <Card className="p-6">
            <div className="flex flex-col lg:flex-row gap-4 items-end">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Search Users
                </label>
                <Input
                  placeholder="Search by name, email, school, or class..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  icon={<TbSearch />}
                />
              </div>

              <div className="w-full lg:w-48">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Filter by Status
                </label>
                <select
                  value={filterStatus}
                  onChange={(e) => setFilterStatus(e.target.value)}
                  className="input-modern"
                >
                  <option value="all">All Users</option>
                  <option value="active">Active Only</option>
                  <option value="blocked">Blocked Only</option>
                </select>
              </div>

              <div className="w-full lg:w-48">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Filter by Plan
                </label>
                <select
                  value={filterSubscription}
                  onChange={(e) => setFilterSubscription(e.target.value)}
                  className="input-modern"
                >
                  <option value="all">All Plans</option>
                  <option value="on-plan">On Plan</option>
                  <option value="expired-plan">Expired Plan</option>
                  <option value="no-plan">No Plan</option>
                </select>
              </div>

              <Button
                variant="secondary"
                icon={<TbFilter />}
                onClick={() => {
                  setSearchQuery("");
                  setFilterStatus("all");
                  setFilterSubscription("all");
                }}
              >
                Clear Filters
              </Button>
            </div>

            {(searchQuery || filterStatus !== "all" || filterSubscription !== "all") && (
              <div className="mt-4 pt-4 border-t border-gray-100">
                <span className="text-sm text-gray-600">
                  Showing {filteredUsers.length} of {users.length} users
                  {filterSubscription !== "all" && (
                    <span className="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs">
                      {filterSubscription === 'on-plan' && 'On Plan'}
                      {filterSubscription === 'expired-plan' && 'Expired Plan'}
                      {filterSubscription === 'no-plan' && 'No Plan'}
                    </span>
                  )}
                </span>
              </div>
            )}
          </Card>
        </motion.div>

        {/* Users Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
        >
          {loading ? (
            <div className="flex justify-center py-12">
              <Loading text="Loading users..." />
            </div>
          ) : filteredUsers.length > 0 ? (
            <div className="space-y-4">
              {filteredUsers.map((user, index) => (
                <motion.div
                  key={user.studentId}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <UserCard user={user} />
                </motion.div>
              ))}
            </div>
          ) : (
            <Card className="p-12 text-center">
              <TbUsers className="w-16 h-16 text-gray-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold text-gray-900 mb-2">No Users Found</h3>
              <p className="text-gray-600">
                {searchQuery || filterStatus !== "all" || filterSubscription !== "all"
                  ? "Try adjusting your search or filter criteria"
                  : "No users have been registered yet"}
              </p>
            </Card>
          )}
        </motion.div>
      </div>
    </div>
  );
}

export default Users;
